#!/bin/bash
# Zenera Setup Script
# Tham khảo từ Medoo patterns

cwd=$(pwd)

echo "🚀 Setting up Zenera Development Environment..."

# Function to check if command exists
command_exists() {
  command -v "$1" >/dev/null 2>&1
}

# Check required tools
echo "🔍 Checking required tools..."

if ! command_exists node; then
  echo "❌ Node.js is not installed. Please install Node.js 18+ first."
  exit 1
else
  NODE_VERSION=$(node --version)
  echo "✅ Node.js found: $NODE_VERSION"
fi

if ! command_exists pnpm; then
  echo "📦 Installing pnpm..."
  npm install -g pnpm
  if [ $? -eq 0 ]; then
    echo "✅ pnpm installed successfully"
  else
    echo "❌ Failed to install pnpm"
    exit 1
  fi
else
  PNPM_VERSION=$(pnpm --version)
  echo "✅ pnpm found: $PNPM_VERSION"
fi

# Install dependencies
echo "📦 Installing dependencies..."
pnpm install

if [ $? -eq 0 ]; then
  echo "✅ Dependencies installed successfully"
else
  echo "❌ Failed to install dependencies"
  exit 1
fi

# Build sharing package first
echo "🏗️  Building shared packages..."
cd ${cwd}/packages/sharing
pnpm build

cd ${cwd}/packages/ui-components
pnpm build

echo "🎉 Setup completed successfully!"
echo ""
echo "📋 Next steps:"
echo "  1. Run 'bin/start' to start all services"
echo "  2. Run 'bin/start_frontend' to start frontend only"
echo "  3. Run 'bin/start_backend' to start backend only"
echo ""
echo "🌐 Default URLs:"
echo "  Frontend: http://localhost:3002"
echo "  Backend: http://localhost:3001"
echo "  API Docs: http://localhost:3001/api"
