'use client';

import { useState } from 'react';
import { Button } from '@zenera/ui-components';
import { Card, CardContent, CardHeader, CardTitle } from '@zenera/ui-components';
import { api } from '@/lib/api';
import { useAuth } from '@/lib/hooks/use-auth';

export default function ApiTestPage() {
  const [healthStatus, setHealthStatus] = useState<any>(null);
  const [products, setProducts] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Use auth store
  const auth = useAuth();

  const testHealthCheck = async () => {
    setLoading(true);
    setError(null);
    try {
      const result = await api.health.check();
      setHealthStatus(result);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const testProductsAPI = async () => {
    setLoading(true);
    setError(null);
    try {
      const result = await api.products.getProducts({ limit: 5 });
      setProducts(result.products || []);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const testAuthAPI = async () => {
    setLoading(true);
    setError(null);
    try {
      // Test registration với auth store
      const timestamp = Date.now();
      const registerResult = await auth.register({
        email: `test${timestamp}@example.com`,
        password: 'password123',
        first_name: 'Test',
        last_name: 'User',
        username: `test${timestamp}`,
        role: 'customer',
      });

      console.log('Registration successful:', registerResult);
      console.log('Auth state after registration:', {
        user: auth.user,
        isAuthenticated: auth.isAuthenticated,
        permissions: auth.userPermissions,
        roles: auth.userRoles,
      });

      setError(null);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const testAuthStoreLogin = async () => {
    setLoading(true);
    setError(null);
    try {
      // Test login với existing user
      const loginResult = await auth.login({
        email: '<EMAIL>', // User đã tạo trước đó
        password: 'password123',
      });

      console.log('Login successful:', loginResult);
      console.log('Auth state after login:', {
        user: auth.user,
        isAuthenticated: auth.isAuthenticated,
        permissions: auth.userPermissions,
        roles: auth.userRoles,
        deviceUuid: auth.deviceUuid,
      });

      setError(null);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const testLogout = async () => {
    setLoading(true);
    setError(null);
    try {
      await auth.logout();
      console.log('Logout successful');
      console.log('Auth state after logout:', {
        user: auth.user,
        isAuthenticated: auth.isAuthenticated,
        permissions: auth.userPermissions,
        roles: auth.userRoles,
      });

      setError(null);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen p-8 bg-background">
      <div className="max-w-4xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold text-foreground">
            API Integration Test
          </h1>
          <p className="text-lg text-muted-foreground">
            Day 5: Testing Frontend-Backend Connectivity
          </p>
        </div>

        {/* Error Display */}
        {error && (
          <Card className="border-destructive">
            <CardContent className="pt-6">
              <div className="text-destructive">
                <strong>Error:</strong> {error}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Health Check Test */}
        <Card>
          <CardHeader>
            <CardTitle>Health Check Test</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button 
              onClick={testHealthCheck} 
              loading={loading}
              className="w-full"
            >
              Test Health Check
            </Button>
            
            {healthStatus && (
              <div className="p-4 bg-muted rounded-lg">
                <pre className="text-sm">
                  {JSON.stringify(healthStatus, null, 2)}
                </pre>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Products API Test */}
        <Card>
          <CardHeader>
            <CardTitle>Products API Test</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button 
              onClick={testProductsAPI} 
              loading={loading}
              className="w-full"
            >
              Test Products API
            </Button>
            
            {products.length > 0 && (
              <div className="space-y-2">
                <h3 className="font-semibold">Products ({products.length}):</h3>
                <div className="p-4 bg-muted rounded-lg max-h-64 overflow-y-auto">
                  <pre className="text-sm">
                    {JSON.stringify(products, null, 2)}
                  </pre>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Auth Store Test */}
        <Card>
          <CardHeader>
            <CardTitle>Auth Store Test (Day 6)</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Current Auth State */}
            <div className="p-4 bg-muted rounded-lg">
              <h4 className="font-semibold mb-2">Current Auth State:</h4>
              <div className="text-sm space-y-1">
                <div><strong>Authenticated:</strong> {auth.isAuthenticated ? 'Yes' : 'No'}</div>
                <div><strong>Loading:</strong> {auth.isLoading ? 'Yes' : 'No'}</div>
                <div><strong>Initializing:</strong> {auth.isInitializing ? 'Yes' : 'No'}</div>
                <div><strong>User:</strong> {auth.user?.email || 'None'}</div>
                <div><strong>Roles:</strong> {auth.userRoles.join(', ') || 'None'}</div>
                <div><strong>Permissions:</strong> {auth.userPermissions.length} permissions</div>
                <div><strong>Device UUID:</strong> {auth.deviceUuid || 'Not set'}</div>
              </div>
            </div>

            {/* Auth Actions */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Button
                onClick={testAuthAPI}
                loading={loading}
                variant="default"
              >
                Register New User
              </Button>

              <Button
                onClick={testAuthStoreLogin}
                loading={loading}
                variant="secondary"
              >
                Login Existing User
              </Button>

              <Button
                onClick={testLogout}
                loading={loading}
                variant="destructive"
                disabled={!auth.isAuthenticated}
              >
                Logout
              </Button>
            </div>

            <div className="text-sm text-muted-foreground">
              Test Zustand auth store với Medoo patterns. Check browser console for detailed results.
            </div>
          </CardContent>
        </Card>

        {/* Auth API Test */}
        <Card>
          <CardHeader>
            <CardTitle>Direct API Test</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button
              onClick={testAuthAPI}
              loading={loading}
              className="w-full"
            >
              Test Direct API (Register + Profile)
            </Button>

            <div className="text-sm text-muted-foreground">
              This tests direct API calls without auth store. Check browser console for results.
            </div>
          </CardContent>
        </Card>

        {/* API Client Info */}
        <Card>
          <CardHeader>
            <CardTitle>API Configuration</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <div><strong>API URL:</strong> {process.env.NEXT_PUBLIC_API_URL}</div>
              <div><strong>API Version:</strong> {process.env.NEXT_PUBLIC_API_VERSION}</div>
              <div><strong>Auth Token:</strong> {api.auth.getToken() ? 'Present' : 'Not set'}</div>
              <div><strong>Authenticated:</strong> {api.auth.isAuthenticated() ? 'Yes' : 'No'}</div>
            </div>
          </CardContent>
        </Card>

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>Test Instructions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <p>1. <strong>Health Check:</strong> Tests basic API connectivity</p>
              <p>2. <strong>Products API:</strong> Tests product listing (may be empty initially)</p>
              <p>3. <strong>Auth API:</strong> Tests user registration and profile fetching</p>
              <p>4. Open browser console to see detailed API responses</p>
              <p>5. Check Network tab to see actual HTTP requests</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
