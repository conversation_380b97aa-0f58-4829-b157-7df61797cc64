/**
 * Stores Test Page
 * Test các Zustand stores đã implement
 */

'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@zenera/ui-components';
import { Button } from '@zenera/ui-components';
import { Input } from '@zenera/ui-components';
import {
  useCartStore,
  useProductsStore,
  usePreferencesStore,
  useAuth
} from '@/lib/stores';

export default function StoresTestPage() {
  const [testProduct] = useState({
    id: 'test-product-1',
    name: 'Test Product',
    price: 29.99,
    originalPrice: 39.99,
    images: ['/placeholder-product.jpg'],
    seller: {
      id: 'test-seller-1',
      name: 'Test Seller',
    },
    stock: 10,
    isAvailable: true,
  });

  // Cart Store
  const {
    items: cartItems,
    summary: cartSummary,
    addItem,
    updateQuantity,
    removeItem,
    clearCart,
    isLoading: cartLoading,
  } = useCartStore();

  // Products Store
  const {
    products,
    isLoading: productsLoading,
    fetchProducts,
    searchProducts,
    searchResults,
    searchQuery,
  } = useProductsStore();

  // Preferences Store
  const {
    theme,
    language,
    currency,
    setTheme,
    toggleThemeMode,
    setCurrency,
    recentlyViewed,
    addRecentlyViewed,
  } = usePreferencesStore();

  // Auth Store
  const { user, isAuthenticated } = useAuth();

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">
          Day 9: State Management Test
        </h1>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Cart Store Test */}
          <Card>
            <CardHeader>
              <CardTitle>🛒 Cart Store</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <h4 className="font-medium">Cart Summary:</h4>
                <div className="text-sm space-y-1">
                  <div>Items: {cartSummary.itemCount}</div>
                  <div>Unique Items: {cartSummary.uniqueItemCount}</div>
                  <div>Subtotal: ${cartSummary.subtotal.toFixed(2)}</div>
                  <div>Total: ${cartSummary.total.toFixed(2)}</div>
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="font-medium">Cart Items:</h4>
                {cartItems.length === 0 ? (
                  <p className="text-sm text-muted-foreground">Cart is empty</p>
                ) : (
                  <div className="space-y-2">
                    {cartItems.map((item) => (
                      <div key={item.id} className="flex items-center justify-between p-2 border rounded">
                        <div className="text-sm">
                          <div>{item.name}</div>
                          <div className="text-muted-foreground">
                            ${item.price} x {item.quantity}
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => updateQuantity(item.id, item.quantity - 1)}
                          >
                            -
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => updateQuantity(item.id, item.quantity + 1)}
                          >
                            +
                          </Button>
                          <Button
                            size="sm"
                            variant="destructive"
                            onClick={() => removeItem(item.id)}
                          >
                            Remove
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              <div className="flex gap-2">
                <Button
                  onClick={() => addItem(testProduct, 1)}
                  loading={cartLoading}
                >
                  Add Test Product
                </Button>
                <Button
                  variant="outline"
                  onClick={clearCart}
                  disabled={cartItems.length === 0}
                >
                  Clear Cart
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Products Store Test */}
          <Card>
            <CardHeader>
              <CardTitle>📦 Products Store</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <h4 className="font-medium">Products ({products.length}):</h4>
                <div className="text-sm">
                  <div>Loading: {productsLoading ? 'Yes' : 'No'}</div>
                  <div>Search Query: {searchQuery || 'None'}</div>
                  <div>Search Results: {searchResults.length}</div>
                </div>
              </div>

              <div className="space-y-2">
                <Input
                  placeholder="Search products..."
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      const query = (e.target as HTMLInputElement).value;
                      searchProducts(query);
                    }
                  }}
                />
              </div>

              <div className="flex gap-2">
                <Button
                  onClick={() => fetchProducts()}
                  loading={productsLoading}
                >
                  Fetch Products
                </Button>
                <Button
                  variant="outline"
                  onClick={() => searchProducts('test')}
                >
                  Search "test"
                </Button>
              </div>

              {products.length > 0 && (
                <div className="space-y-2">
                  <h4 className="font-medium">Sample Products:</h4>
                  <div className="max-h-40 overflow-y-auto space-y-1">
                    {products.slice(0, 5).map((product) => (
                      <div key={product.id} className="text-sm p-2 border rounded">
                        <div>{product.name}</div>
                        <div className="text-muted-foreground">${product.price}</div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Preferences Store Test */}
          <Card>
            <CardHeader>
              <CardTitle>⚙️ Preferences Store</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <h4 className="font-medium">Current Settings:</h4>
                <div className="text-sm space-y-1">
                  <div>Theme: {theme.mode}</div>
                  <div>Language: {language.name} ({language.code})</div>
                  <div>Currency: {currency.name} ({currency.symbol})</div>
                  <div>Recently Viewed: {recentlyViewed.length} items</div>
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="font-medium">Theme Controls:</h4>
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={toggleThemeMode}
                  >
                    Toggle Theme
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setTheme({ primaryColor: '#ef4444' })}
                  >
                    Red Theme
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setTheme({ primaryColor: '#3b82f6' })}
                  >
                    Blue Theme
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="font-medium">Currency Controls:</h4>
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setCurrency({ code: 'USD', symbol: '$', name: 'US Dollar' })}
                  >
                    USD
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setCurrency({ code: 'VND', symbol: '₫', name: 'Vietnamese Dong' })}
                  >
                    VND
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setCurrency({ code: 'EUR', symbol: '€', name: 'Euro' })}
                  >
                    EUR
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <Button
                  onClick={() => addRecentlyViewed('test-product-' + Date.now())}
                >
                  Add Recently Viewed
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Auth Store Test */}
          <Card>
            <CardHeader>
              <CardTitle>🔐 Auth Store</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <h4 className="font-medium">Authentication Status:</h4>
                <div className="text-sm space-y-1">
                  <div>Authenticated: {isAuthenticated ? 'Yes' : 'No'}</div>
                  {user && (
                    <>
                      <div>User: {user.first_name} {user.last_name}</div>
                      <div>Email: {user.email}</div>
                      <div>Roles: {user.roles?.join(', ')}</div>
                    </>
                  )}
                </div>
              </div>

              {!isAuthenticated && (
                <div className="text-sm text-muted-foreground">
                  Please login to see user information
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Store Integration Test */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>🔗 Store Integration Test</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="text-sm">
                <h4 className="font-medium mb-2">Integration Status:</h4>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="p-3 border rounded">
                    <div className="font-medium">Cart Store</div>
                    <div className="text-green-600">✅ Working</div>
                  </div>
                  <div className="p-3 border rounded">
                    <div className="font-medium">Products Store</div>
                    <div className="text-green-600">✅ Working</div>
                  </div>
                  <div className="p-3 border rounded">
                    <div className="font-medium">Preferences Store</div>
                    <div className="text-green-600">✅ Working</div>
                  </div>
                  <div className="p-3 border rounded">
                    <div className="font-medium">Auth Store</div>
                    <div className="text-green-600">✅ Working</div>
                  </div>
                </div>
              </div>

              <div className="flex gap-4">
                <Button
                  onClick={() => {
                    console.log('Store States:', {
                      cart: { items: cartItems.length, total: cartSummary.total },
                      products: { count: products.length, loading: productsLoading },
                      preferences: { theme: theme.mode, currency: currency.code },
                      auth: { authenticated: isAuthenticated, user: user?.email },
                    });
                  }}
                >
                  Log Store States
                </Button>

                <Button
                  variant="outline"
                  onClick={() => {
                    // Test store integration
                    addItem(testProduct);
                    addRecentlyViewed(testProduct.id);
                  }}
                >
                  Test Integration
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
