'use client';

import { useEffect } from 'react';
import { Button } from '@zenera/ui-components';
import { AlertTriangle, RefreshCw, Home } from 'lucide-react';

/**
 * Global Error Component
 * This component is rendered when an error occurs at the root level
 */
export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Log the error to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('Global Error:', error);
    }

    // In production, you might want to log this to an error reporting service
    // Example: Sentry.captureException(error);
  }, [error]);

  const handleGoHome = () => {
    window.location.href = '/';
  };

  return (
    <html>
      <body>
        <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
          <div className="max-w-lg w-full bg-white rounded-lg shadow-lg p-8 text-center">
            <div className="flex justify-center mb-6">
              <AlertTriangle className="h-20 w-20 text-red-500" />
            </div>
            
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              Something went wrong!
            </h1>
            
            <p className="text-gray-600 mb-6 text-lg">
              We apologize for the inconvenience. An unexpected error has occurred.
            </p>

            {process.env.NODE_ENV === 'development' && (
              <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md text-left">
                <h3 className="text-sm font-medium text-red-800 mb-2">Error Details:</h3>
                <pre className="text-xs text-red-700 whitespace-pre-wrap overflow-auto max-h-40">
                  {error.message}
                  {error.digest && `\nDigest: ${error.digest}`}
                  {error.stack}
                </pre>
              </div>
            )}

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                onClick={reset}
                className="flex items-center justify-center gap-2 px-6 py-3"
                size="lg"
              >
                <RefreshCw className="h-5 w-5" />
                Try Again
              </Button>
              
              <Button
                onClick={handleGoHome}
                className="flex items-center justify-center gap-2 px-6 py-3"
                variant="outline"
                size="lg"
              >
                <Home className="h-5 w-5" />
                Go to Homepage
              </Button>
            </div>

            <div className="mt-8 pt-6 border-t border-gray-200">
              <p className="text-sm text-gray-500">
                If this problem persists, please contact our support team.
              </p>
            </div>
          </div>
        </div>
      </body>
    </html>
  );
}
