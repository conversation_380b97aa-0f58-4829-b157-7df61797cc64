import { Button } from '@zenera/ui-components';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@zenera/ui-components';
import { Input } from '@zenera/ui-components';
import { ProductCard } from '@zenera/ui-components';

// Mock product data for demo
const mockProduct = {
  id: '1',
  name: 'Premium Wireless Headphones',
  price: 299.99,
  originalPrice: 399.99,
  image: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=400&h=400&fit=crop',
  rating: 4.5,
  reviewCount: 128,
  badge: 'Best Seller',
  inStock: true,
};

export default function DesignSystemDemo() {
  return (
    <div className="min-h-screen bg-neutral-50 p-8">
      <div className="max-w-6xl mx-auto space-y-12">
        {/* Header */}
        <div className="text-center space-y-4 animate-fade-in">
          <h1 className="text-5xl font-bold font-display text-neutral-900">
            Zenera Design System
          </h1>
          <p className="text-xl text-neutral-600 max-w-2xl mx-auto">
            Production-tested components với custom animations cho e-commerce platform
          </p>
        </div>

        {/* Buttons Demo */}
        <Card className="animate-slide-in-up">
          <CardHeader>
            <CardTitle>Button Components</CardTitle>
            <CardDescription>
              Various button styles với custom animations và e-commerce variants
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex flex-wrap gap-4">
              <Button variant="default">Default Button</Button>
              <Button variant="secondary">Secondary</Button>
              <Button variant="outline">Outline</Button>
              <Button variant="ghost">Ghost</Button>
              <Button variant="cart">Add to Cart</Button>
              <Button variant="buy">Buy Now</Button>
              <Button variant="wishlist">♡ Wishlist</Button>
            </div>
            
            <div className="flex flex-wrap gap-4">
              <Button size="sm">Small</Button>
              <Button size="default">Default</Button>
              <Button size="lg">Large</Button>
              <Button size="xl">Extra Large</Button>
            </div>

            <div className="flex flex-wrap gap-4">
              <Button animation="lift">Lift Animation</Button>
              <Button animation="bounce">Bounce</Button>
              <Button animation="glow">Glow Effect</Button>
              <Button loading>Loading...</Button>
            </div>
          </CardContent>
        </Card>

        {/* Input Demo */}
        <Card className="animate-slide-in-up" style={{ animationDelay: '100ms' }}>
          <CardHeader>
            <CardTitle>Input Components</CardTitle>
            <CardDescription>
              Form inputs với validation states và animations
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Input 
                label="Email Address" 
                placeholder="Enter your email"
                type="email"
              />
              <Input 
                label="Password" 
                placeholder="Enter password"
                type="password"
              />
              <Input 
                label="Success State" 
                placeholder="Valid input"
                success="Email is available!"
              />
              <Input 
                label="Error State" 
                placeholder="Invalid input"
                error="This field is required"
              />
            </div>
          </CardContent>
        </Card>

        {/* Product Card Demo */}
        <Card className="animate-slide-in-up" style={{ animationDelay: '200ms' }}>
          <CardHeader>
            <CardTitle>Product Card Component</CardTitle>
            <CardDescription>
              E-commerce product card với hover effects và interactive elements
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <ProductCard 
                product={mockProduct}
                variant="default"
                onAddToCart={(id) => console.log('Add to cart:', id)}
                onAddToWishlist={(id) => console.log('Add to wishlist:', id)}
                onQuickView={(id) => console.log('Quick view:', id)}
              />
              <ProductCard 
                product={{
                  ...mockProduct,
                  id: '2',
                  name: 'Smart Watch Pro',
                  price: 199.99,
                  image: 'https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400&h=400&fit=crop',
                  badge: 'New',
                }}
                variant="featured"
                onAddToCart={(id) => console.log('Add to cart:', id)}
              />
              <ProductCard 
                product={{
                  ...mockProduct,
                  id: '3',
                  name: 'Laptop Stand',
                  price: 49.99,
                  originalPrice: undefined,
                  image: 'https://images.unsplash.com/photo-1527864550417-7fd91fc51a46?w=400&h=400&fit=crop',
                  badge: undefined,
                  inStock: false,
                }}
                variant="compact"
              />
            </div>
          </CardContent>
        </Card>

        {/* Animation Demo */}
        <Card className="animate-slide-in-up" style={{ animationDelay: '300ms' }}>
          <CardHeader>
            <CardTitle>Animation Showcase</CardTitle>
            <CardDescription>
              Custom animations cho enhanced user experience
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="p-6 bg-primary-50 rounded-lg text-center animate-fade-in">
                <div className="w-12 h-12 bg-primary-500 rounded-full mx-auto mb-2"></div>
                <p className="text-sm font-medium">Fade In</p>
              </div>
              <div className="p-6 bg-success-50 rounded-lg text-center animate-slide-in-up">
                <div className="w-12 h-12 bg-success-500 rounded-full mx-auto mb-2"></div>
                <p className="text-sm font-medium">Slide Up</p>
              </div>
              <div className="p-6 bg-warning-50 rounded-lg text-center animate-scale-in">
                <div className="w-12 h-12 bg-warning-500 rounded-full mx-auto mb-2"></div>
                <p className="text-sm font-medium">Scale In</p>
              </div>
              <div className="p-6 bg-error-50 rounded-lg text-center hover:animate-add-to-cart cursor-pointer">
                <div className="w-12 h-12 bg-error-500 rounded-full mx-auto mb-2"></div>
                <p className="text-sm font-medium">Hover Me</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="text-center py-8 animate-fade-in" style={{ animationDelay: '400ms' }}>
          <p className="text-neutral-600">
            🎨 Zenera Design System - Built with Next.js 15.x + Tailwind CSS + Custom Animations
          </p>
        </div>
      </div>
    </div>
  );
}
