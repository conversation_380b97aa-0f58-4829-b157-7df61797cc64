/**
 * Profile Page
 * Protected route for user profile management
 */

'use client';

import { ProtectedRoute } from '@/components/auth/protected-route';
import { UserProfile } from '@/components/auth/user-profile';

export default function ProfilePage() {
  return (
    <ProtectedRoute>
      <div className="container mx-auto py-8 px-4">
        <div className="max-w-2xl mx-auto">
          <h1 className="text-3xl font-bold mb-8">My Profile</h1>
          <UserProfile />
        </div>
      </div>
    </ProtectedRoute>
  );
}
