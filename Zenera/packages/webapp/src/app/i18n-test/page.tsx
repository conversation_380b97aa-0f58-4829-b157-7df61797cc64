"use client";

import { useState } from "react";
import { useZeneraTranslation, useFormTranslation, useContentByLocale, useDataContentByLocale } from "@/lib/hooks/use-translation";
import { useLocale, useLocaleSwitch, useLocaleUtils } from "@/lib/hooks/use-locale";
import LanguageSwitcher, { CompactLanguageSwitcher, FormLanguageSwitcher } from "@/components/i18n/language-switcher";
import ZeneraTrans, { RichTrans, useGetTranslatedContent } from "@/components/i18n/trans";
import { getZeneraTranslationFunc } from "@/lib/i18n/get-translation-func";
import { Languages } from "@/lib/constants/languages";

/**
 * i18n Test Page
 * Test all i18n functionality
 */
export default function I18nTestPage() {
  const { t, isReady, currentLanguage } = useZeneraTranslation();
  const { t: tForm } = useFormTranslation();
  const locale = useLocale();
  const { switchLocale } = useLocaleSwitch();
  const { formatCurrency, formatDate, formatNumber, formatRelativeTime } = useLocaleUtils();
  const getContentByLocale = useContentByLocale();
  const getDataContentByLocale = useDataContentByLocale();
  const getTranslatedContent = useGetTranslatedContent();
  
  const [formLocale, setFormLocale] = useState(locale);
  const [testAmount] = useState(1234567.89);
  const [testDate] = useState(new Date());
  const [testNumber] = useState(123456);

  const multiLanguageContent = {
    en: "This is English content",
    vi: "Đây là nội dung tiếng Việt"
  };

  if (!isReady) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading translations...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                {t("app.name")} - i18n Test
              </h1>
              <p className="text-gray-600 mt-2">
                {t("app.description")}
              </p>
            </div>
            <LanguageSwitcher />
          </div>
        </div>

        {/* Current State */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Current State</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Current Locale
              </label>
              <p className="text-lg font-mono bg-gray-100 px-3 py-2 rounded">
                {locale}
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                i18n Language
              </label>
              <p className="text-lg font-mono bg-gray-100 px-3 py-2 rounded">
                {currentLanguage}
              </p>
            </div>
          </div>
        </div>

        {/* Language Switcher Variants */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Language Switcher Variants</h2>
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-2">Dropdown (Default)</h3>
              <LanguageSwitcher variant="dropdown" />
            </div>
            
            <div>
              <h3 className="text-lg font-medium mb-2">Buttons</h3>
              <LanguageSwitcher variant="buttons" />
            </div>
            
            <div>
              <h3 className="text-lg font-medium mb-2">Minimal</h3>
              <LanguageSwitcher variant="minimal" />
            </div>
            
            <div>
              <h3 className="text-lg font-medium mb-2">Compact</h3>
              <CompactLanguageSwitcher />
            </div>
            
            <div>
              <h3 className="text-lg font-medium mb-2">Form Switcher</h3>
              <FormLanguageSwitcher 
                value={formLocale} 
                onChange={setFormLocale}
              />
              <p className="text-sm text-gray-600 mt-2">
                Selected: {formLocale}
              </p>
            </div>
          </div>
        </div>

        {/* Basic Translations */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Basic Translations</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="font-medium mb-2">Navigation</h3>
              <ul className="space-y-1 text-sm">
                <li>• {t("navigation.home")}</li>
                <li>• {t("navigation.products")}</li>
                <li>• {t("navigation.cart")}</li>
                <li>• {t("navigation.account")}</li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-medium mb-2">Actions</h3>
              <ul className="space-y-1 text-sm">
                <li>• {t("actions.save")}</li>
                <li>• {t("actions.cancel")}</li>
                <li>• {t("actions.delete")}</li>
                <li>• {t("actions.edit")}</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Form Translations */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Form Translations</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="font-medium mb-2">Labels</h3>
              <ul className="space-y-1 text-sm">
                <li>• {tForm("labels.email")}</li>
                <li>• {tForm("labels.password")}</li>
                <li>• {tForm("labels.firstName")}</li>
                <li>• {tForm("labels.lastName")}</li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-medium mb-2">Buttons</h3>
              <ul className="space-y-1 text-sm">
                <li>• {tForm("buttons.submit")}</li>
                <li>• {tForm("buttons.save")}</li>
                <li>• {tForm("buttons.login")}</li>
                <li>• {tForm("buttons.register")}</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Interpolation */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Interpolation</h2>
          <div className="space-y-2">
            <p>
              <strong>With parameters:</strong> {t("messages.passwordTooShort", { minLength: 8 })}
            </p>
            <p>
              <strong>File size:</strong> {t("messages.fileTooLarge", { maxSize: "10MB" })}
            </p>
            <p>
              <strong>Language changed:</strong> {t("language.changed", { language: currentLanguage })}
            </p>
          </div>
        </div>

        {/* Formatting */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Locale-based Formatting</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="font-medium mb-2">Numbers & Currency</h3>
              <ul className="space-y-1 text-sm">
                <li>• Number: {formatNumber(testNumber)}</li>
                <li>• Currency: {formatCurrency(testAmount)}</li>
              </ul>
            </div>
            
            <div>
              <h3 className="font-medium mb-2">Dates & Time</h3>
              <ul className="space-y-1 text-sm">
                <li>• Date: {formatDate(testDate)}</li>
                <li>• Relative: {formatRelativeTime(new Date(Date.now() - 2 * 60 * 60 * 1000))}</li>
              </ul>
            </div>
          </div>
        </div>

        {/* Multi-language Content */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Multi-language Content (Medoo Pattern)</h2>
          <div className="space-y-4">
            <div className="bg-gray-50 p-4 rounded">
              <p className="font-medium mb-2">Content Object:</p>
              <pre className="text-sm text-gray-600 mb-4">
                {JSON.stringify(multiLanguageContent, null, 2)}
              </pre>
              <p className="font-medium mb-2">Using getContentByLocale ({locale}):</p>
              <p className="text-lg">{getContentByLocale(multiLanguageContent)}</p>
            </div>

            <div className="bg-blue-50 p-4 rounded">
              <p className="font-medium mb-2">Using getDataContentByLocale:</p>
              <p className="text-lg">{getDataContentByLocale(multiLanguageContent)}</p>
            </div>
          </div>
        </div>

        {/* Trans Component Tests */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Trans Component (Medoo Pattern)</h2>
          <div className="space-y-4">
            <div className="bg-gray-50 p-4 rounded">
              <p className="font-medium mb-2">Basic Trans:</p>
              <ZeneraTrans i18nKey="app.welcome" />
            </div>

            <div className="bg-green-50 p-4 rounded">
              <p className="font-medium mb-2">Trans with interpolation:</p>
              <ZeneraTrans
                i18nKey="language.changed"
                values={{ language: currentLanguage }}
              />
            </div>

            <div className="bg-purple-50 p-4 rounded">
              <p className="font-medium mb-2">Rich Trans with components:</p>
              <RichTrans
                i18nKey="messages.passwordTooShort"
                values={{ minLength: 8 }}
              />
            </div>
          </div>
        </div>

        {/* Status Messages */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Status Messages</h2>
          <div className="space-y-3">
            <div className="flex items-center space-x-3">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span>{t("messages.operationCompleted")}</span>
            </div>
            <div className="flex items-center space-x-3">
              <div className="w-3 h-3 bg-red-500 rounded-full"></div>
              <span>{t("messages.operationFailed")}</span>
            </div>
            <div className="flex items-center space-x-3">
              <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
              <span>{t("messages.unsavedChanges")}</span>
            </div>
            <div className="flex items-center space-x-3">
              <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
              <span>{t("messages.loadingData")}</span>
            </div>
          </div>
        </div>

        {/* Manual Language Switch */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-xl font-semibold mb-4">Manual Language Switch</h2>
          <div className="flex space-x-4">
            <button
              onClick={() => switchLocale(Languages.EN)}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
            >
              Switch to English
            </button>
            <button
              onClick={() => switchLocale(Languages.VI)}
              className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700 transition-colors"
            >
              Chuyển sang Tiếng Việt
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
