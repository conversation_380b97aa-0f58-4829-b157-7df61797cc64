/**
 * Utilities Test Page
 * Test các utilities đã extract từ Medoo
 */

'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@zenera/ui-components';
import { Button } from '@zenera/ui-components';
import { Input } from '@zenera/ui-components';
import { 
  CurrencyUtils, 
  PriceUtils, 
  ZeneraCurrency,
  DateUtils,
  TimeUtils,
  EcommerceDateUtils,
  StringUtils,
  EcommerceStringUtils,
  ValidationUtils,
  EcommerceValidationUtils
} from '@zenera/sharing';

export default function UtilsTestPage() {
  const [testInputs, setTestInputs] = useState({
    price: '1234.56',
    currency: ZeneraCurrency.USD,
    dateString: '2024-06-02',
    textInput: 'Hello World Test String',
    email: '<EMAIL>',
    password: 'TestPassword123!',
  });

  const handleInputChange = (field: string, value: string) => {
    setTestInputs(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">
          Day 8: Common Utilities Test
        </h1>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Currency Utils Test */}
          <Card>
            <CardHeader>
              <CardTitle>💰 Currency Utilities</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium">Price:</label>
                <Input
                  value={testInputs.price}
                  onChange={(e) => handleInputChange('price', e.target.value)}
                  placeholder="Enter price"
                />
              </div>

              <div>
                <label className="text-sm font-medium">Currency:</label>
                <select
                  value={testInputs.currency}
                  onChange={(e) => handleInputChange('currency', e.target.value)}
                  className="w-full px-3 py-2 border border-input bg-background rounded-md text-sm"
                >
                  {Object.values(ZeneraCurrency).map(currency => (
                    <option key={currency} value={currency}>{currency}</option>
                  ))}
                </select>
              </div>

              <div className="space-y-2 text-sm">
                <div><strong>Formatted:</strong> {CurrencyUtils.format(parseFloat(testInputs.price) || 0, testInputs.currency)}</div>
                <div><strong>With Symbol (Start):</strong> {CurrencyUtils.formatWithSymbol(parseFloat(testInputs.price) || 0, testInputs.currency, 'start')}</div>
                <div><strong>With Symbol (End):</strong> {CurrencyUtils.formatWithSymbol(parseFloat(testInputs.price) || 0, testInputs.currency, 'end')}</div>
                <div><strong>Number Only:</strong> {CurrencyUtils.formatNumber(parseFloat(testInputs.price) || 0, testInputs.currency)}</div>
                <div><strong>Rounded:</strong> {CurrencyUtils.round(parseFloat(testInputs.price) || 0, testInputs.currency)}</div>
                <div><strong>Symbol:</strong> {CurrencyUtils.getSymbol(testInputs.currency)}</div>
                <div><strong>Label:</strong> {CurrencyUtils.getLabel(testInputs.currency)}</div>
              </div>

              <div className="border-t pt-4">
                <h4 className="font-medium mb-2">Price Utils:</h4>
                <div className="space-y-1 text-sm">
                  <div><strong>Discount 20%:</strong> {PriceUtils.calculateDiscountPercentage(100, 80)}%</div>
                  <div><strong>Sale Price (20% off):</strong> {PriceUtils.formatPrice(PriceUtils.calculateSalePrice(parseFloat(testInputs.price) || 0, 20), testInputs.currency)}</div>
                  <div><strong>Price Range:</strong> {PriceUtils.formatPriceRange(10, parseFloat(testInputs.price) || 0, testInputs.currency)}</div>
                  <div><strong>Is Free:</strong> {PriceUtils.isFree(parseFloat(testInputs.price) || 0) ? 'Yes' : 'No'}</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Date Utils Test */}
          <Card>
            <CardHeader>
              <CardTitle>📅 Date/Time Utilities</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium">Date:</label>
                <Input
                  type="date"
                  value={testInputs.dateString}
                  onChange={(e) => handleInputChange('dateString', e.target.value)}
                />
              </div>

              <div className="space-y-2 text-sm">
                <div><strong>Current Time:</strong> {TimeUtils.formatTime(TimeUtils.today())}</div>
                <div><strong>Formatted (EN):</strong> {DateUtils.format(testInputs.dateString)}</div>
                <div><strong>Formatted (VN):</strong> {DateUtils.formatVN(testInputs.dateString)}</div>
                <div><strong>Relative:</strong> {DateUtils.formatRelative(testInputs.dateString)}</div>
                <div><strong>Is Valid:</strong> {DateUtils.isValid(testInputs.dateString) ? 'Yes' : 'No'}</div>
                <div><strong>Is Today:</strong> {DateUtils.isToday(testInputs.dateString) ? 'Yes' : 'No'}</div>
                <div><strong>API Format:</strong> {DateUtils.toAPIFormat(testInputs.dateString)}</div>
                <div><strong>ISO String:</strong> {DateUtils.toISOString(testInputs.dateString)}</div>
              </div>

              <div className="border-t pt-4">
                <h4 className="font-medium mb-2">E-commerce Date Utils:</h4>
                <div className="space-y-1 text-sm">
                  <div><strong>Order Date:</strong> {EcommerceDateUtils.formatOrderDate(testInputs.dateString)}</div>
                  <div><strong>Delivery Date:</strong> {EcommerceDateUtils.formatDeliveryDate(testInputs.dateString)}</div>
                  <div><strong>Est. Delivery:</strong> {DateUtils.format(EcommerceDateUtils.calculateEstimatedDelivery(testInputs.dateString, 7) || '')}</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* String Utils Test */}
          <Card>
            <CardHeader>
              <CardTitle>📝 String Utilities</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium">Text:</label>
                <Input
                  value={testInputs.textInput}
                  onChange={(e) => handleInputChange('textInput', e.target.value)}
                  placeholder="Enter text"
                />
              </div>

              <div className="space-y-2 text-sm">
                <div><strong>Slugify:</strong> {StringUtils.slugify(testInputs.textInput)}</div>
                <div><strong>Capitalize:</strong> {StringUtils.capitalize(testInputs.textInput)}</div>
                <div><strong>Capitalize Words:</strong> {StringUtils.capitalizeWords(testInputs.textInput)}</div>
                <div><strong>camelCase:</strong> {StringUtils.toCamelCase(testInputs.textInput)}</div>
                <div><strong>PascalCase:</strong> {StringUtils.toPascalCase(testInputs.textInput)}</div>
                <div><strong>kebab-case:</strong> {StringUtils.toKebabCase(testInputs.textInput)}</div>
                <div><strong>snake_case:</strong> {StringUtils.toSnakeCase(testInputs.textInput)}</div>
                <div><strong>Truncate (10):</strong> {StringUtils.truncate(testInputs.textInput, 10)}</div>
                <div><strong>Word Count:</strong> {StringUtils.wordCount(testInputs.textInput)}</div>
                <div><strong>Is Empty:</strong> {StringUtils.isEmpty(testInputs.textInput) ? 'Yes' : 'No'}</div>
              </div>

              <div className="border-t pt-4">
                <h4 className="font-medium mb-2">E-commerce String Utils:</h4>
                <div className="space-y-1 text-sm">
                  <div><strong>Generate SKU:</strong> {EcommerceStringUtils.generateSKU(testInputs.textInput, 'ZEN')}</div>
                  <div><strong>Product Title:</strong> {EcommerceStringUtils.formatProductTitle(testInputs.textInput)}</div>
                  <div><strong>Product Slug:</strong> {EcommerceStringUtils.generateProductSlug(testInputs.textInput)}</div>
                  <div><strong>Meta Description:</strong> {EcommerceStringUtils.generateMetaDescription(testInputs.textInput)}</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Validation Utils Test */}
          <Card>
            <CardHeader>
              <CardTitle>✅ Validation Utilities</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium">Email:</label>
                <Input
                  value={testInputs.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  placeholder="Enter email"
                />
              </div>

              <div>
                <label className="text-sm font-medium">Password:</label>
                <Input
                  type="password"
                  value={testInputs.password}
                  onChange={(e) => handleInputChange('password', e.target.value)}
                  placeholder="Enter password"
                />
              </div>

              <div className="space-y-2 text-sm">
                <div><strong>Valid Email:</strong> {ValidationUtils.isValidEmail(testInputs.email) ? '✅' : '❌'}</div>
                <div><strong>Valid URL:</strong> {ValidationUtils.isValidUrl(testInputs.textInput) ? '✅' : '❌'}</div>
                <div><strong>Is Required:</strong> {ValidationUtils.isRequired(testInputs.textInput) ? '✅' : '❌'}</div>
                <div><strong>Valid Length (5-20):</strong> {ValidationUtils.isValidLength(testInputs.textInput, 5, 20) ? '✅' : '❌'}</div>
              </div>

              <div className="border-t pt-4">
                <h4 className="font-medium mb-2">Password Validation:</h4>
                <div className="space-y-1 text-sm">
                  {(() => {
                    const result = ValidationUtils.isValidPassword(testInputs.password);
                    return (
                      <>
                        <div><strong>Valid:</strong> {result.isValid ? '✅' : '❌'}</div>
                        {result.errors.length > 0 && (
                          <div>
                            <strong>Errors:</strong>
                            <ul className="list-disc list-inside ml-4">
                              {result.errors.map((error, index) => (
                                <li key={index} className="text-red-600">{error}</li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </>
                    );
                  })()}
                </div>
              </div>

              <div className="border-t pt-4">
                <h4 className="font-medium mb-2">E-commerce Validation:</h4>
                <div className="space-y-1 text-sm">
                  <div><strong>Valid Price (100):</strong> {EcommerceValidationUtils.isValidPrice(100).isValid ? '✅' : '❌'}</div>
                  <div><strong>Valid SKU:</strong> {EcommerceValidationUtils.isValidSKU('TEST-123').isValid ? '✅' : '❌'}</div>
                  <div><strong>Valid Inventory (10):</strong> {EcommerceValidationUtils.isValidInventory(10).isValid ? '✅' : '❌'}</div>
                  <div><strong>Valid Discount (20%):</strong> {EcommerceValidationUtils.isValidDiscountPercentage(20).isValid ? '✅' : '❌'}</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Test Actions */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>🧪 Test Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex gap-4">
              <Button
                onClick={() => {
                  console.log('Currency Utils Test:', {
                    formatted: CurrencyUtils.format(1234.56, ZeneraCurrency.USD),
                    converted: CurrencyUtils.convert(100, ZeneraCurrency.USD, ZeneraCurrency.VND, { USD: 1, VND: 24000 }),
                  });
                }}
              >
                Test Currency Utils
              </Button>

              <Button
                onClick={() => {
                  console.log('Date Utils Test:', {
                    formatted: DateUtils.format(new Date()),
                    relative: DateUtils.formatRelative(new Date(Date.now() - 3600000)), // 1 hour ago
                  });
                }}
              >
                Test Date Utils
              </Button>

              <Button
                onClick={() => {
                  console.log('String Utils Test:', {
                    slug: StringUtils.slugify('Hello World Test'),
                    sku: EcommerceStringUtils.generateSKU('Test Product'),
                  });
                }}
              >
                Test String Utils
              </Button>

              <Button
                onClick={() => {
                  console.log('Validation Utils Test:', {
                    email: ValidationUtils.isValidEmail('<EMAIL>'),
                    password: ValidationUtils.isValidPassword('TestPassword123!'),
                    price: EcommerceValidationUtils.isValidPrice(99.99),
                  });
                }}
              >
                Test Validation Utils
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
