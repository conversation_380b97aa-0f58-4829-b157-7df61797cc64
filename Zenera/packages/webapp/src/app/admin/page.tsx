/**
 * Admin Dashboard Page
 * Protected route for admin users only
 */

'use client';

import { AdminRoute } from '@/components/auth/protected-route';
import { useAuth } from '@/lib/hooks/use-auth';
import { Card, CardContent, CardHeader, CardTitle } from '@zenera/ui-components';
import { Button } from '@zenera/ui-components';

export default function AdminDashboardPage() {
  return (
    <AdminRoute>
      <AdminDashboardContent />
    </AdminRoute>
  );
}

function AdminDashboardContent() {
  const { user } = useAuth();

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">
          Admin Dashboard
        </h1>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* User Management */}
          <Card>
            <CardHeader>
              <CardTitle>User Management</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-4">
                Manage users, roles, and permissions across the platform.
              </p>
              <Button className="w-full">
                Manage Users
              </Button>
            </CardContent>
          </Card>

          {/* Product Management */}
          <Card>
            <CardHeader>
              <CardTitle>Product Management</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-4">
                Oversee all products, categories, and inventory.
              </p>
              <Button className="w-full">
                Manage Products
              </Button>
            </CardContent>
          </Card>

          {/* Order Management */}
          <Card>
            <CardHeader>
              <CardTitle>Order Management</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-4">
                Monitor and manage all orders across the platform.
              </p>
              <Button className="w-full">
                Manage Orders
              </Button>
            </CardContent>
          </Card>

          {/* Analytics */}
          <Card>
            <CardHeader>
              <CardTitle>Analytics & Reports</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-4">
                View comprehensive analytics and generate reports.
              </p>
              <Button className="w-full">
                View Analytics
              </Button>
            </CardContent>
          </Card>

          {/* System Settings */}
          <Card>
            <CardHeader>
              <CardTitle>System Settings</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-4">
                Configure system-wide settings and preferences.
              </p>
              <Button className="w-full">
                System Settings
              </Button>
            </CardContent>
          </Card>

          {/* Seller Approvals */}
          <Card>
            <CardHeader>
              <CardTitle>Seller Approvals</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-4">
                Review and approve seller applications.
              </p>
              <Button className="w-full">
                Review Applications
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Admin Info */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>Admin Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <div><strong>Admin:</strong> {user?.first_name} {user?.last_name}</div>
              <div><strong>Email:</strong> {user?.email}</div>
              <div><strong>Admin Role:</strong> {user?.admin_info?.admin_role}</div>
              <div><strong>Permissions:</strong> {user?.admin_info?.permissions?.join(', ')}</div>
              <div><strong>Assigned Areas:</strong> {user?.admin_info?.assigned_areas?.join(', ')}</div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
