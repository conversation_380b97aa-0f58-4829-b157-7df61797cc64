/**
 * Dashboard Page
 * Protected route for authenticated users
 */

'use client';

import { ProtectedRoute } from '@/components/auth/protected-route';
import { useAuth } from '@/lib/hooks/use-auth';
import { Card, CardContent, CardHeader, CardTitle } from '@zenera/ui-components';
import { Button } from '@zenera/ui-components';

export default function DashboardPage() {
  return (
    <ProtectedRoute>
      <DashboardContent />
    </ProtectedRoute>
  );
}

function DashboardContent() {
  const { user, isAdmin, isSeller, isCustomer } = useAuth();

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">
          Welcome back, {user?.first_name}!
        </h1>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Profile Card */}
          <Card>
            <CardHeader>
              <CardTitle>Profile</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-4">
                Manage your account settings and personal information.
              </p>
              <Button variant="outline" className="w-full">
                View Profile
              </Button>
            </CardContent>
          </Card>

          {/* Customer Features */}
          {isCustomer() && (
            <>
              <Card>
                <CardHeader>
                  <CardTitle>My Orders</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-4">
                    Track your orders and purchase history.
                  </p>
                  <Button variant="outline" className="w-full">
                    View Orders
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Shopping Cart</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-4">
                    Review items in your cart and proceed to checkout.
                  </p>
                  <Button variant="outline" className="w-full">
                    View Cart
                  </Button>
                </CardContent>
              </Card>
            </>
          )}

          {/* Seller Features */}
          {isSeller() && (
            <>
              <Card>
                <CardHeader>
                  <CardTitle>My Products</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-4">
                    Manage your product listings and inventory.
                  </p>
                  <Button variant="outline" className="w-full">
                    Manage Products
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Sales Analytics</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-4">
                    View your sales performance and analytics.
                  </p>
                  <Button variant="outline" className="w-full">
                    View Analytics
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Store Settings</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-4">
                    Configure your store information and settings.
                  </p>
                  <Button variant="outline" className="w-full">
                    Store Settings
                  </Button>
                </CardContent>
              </Card>
            </>
          )}

          {/* Admin Features */}
          {isAdmin() && (
            <>
              <Card>
                <CardHeader>
                  <CardTitle>User Management</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-4">
                    Manage users, roles, and permissions.
                  </p>
                  <Button variant="outline" className="w-full">
                    Manage Users
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>System Analytics</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-4">
                    View system-wide analytics and reports.
                  </p>
                  <Button variant="outline" className="w-full">
                    View Analytics
                  </Button>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>System Settings</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-4">
                    Configure system settings and preferences.
                  </p>
                  <Button variant="outline" className="w-full">
                    System Settings
                  </Button>
                </CardContent>
              </Card>
            </>
          )}
        </div>

        {/* User Info Debug */}
        <Card className="mt-8">
          <CardHeader>
            <CardTitle>Debug Info</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <div><strong>User ID:</strong> {user?.id}</div>
              <div><strong>Email:</strong> {user?.email}</div>
              <div><strong>Roles:</strong> {user?.roles?.join(', ')}</div>
              <div><strong>Is Admin:</strong> {isAdmin() ? 'Yes' : 'No'}</div>
              <div><strong>Is Seller:</strong> {isSeller() ? 'Yes' : 'No'}</div>
              <div><strong>Is Customer:</strong> {isCustomer() ? 'Yes' : 'No'}</div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
