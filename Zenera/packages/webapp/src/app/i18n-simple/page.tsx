"use client";

import { useState } from "react";
import { Languages } from "@/lib/constants/languages";

/**
 * Simple i18n Test Page
 * Basic test without complex hooks
 */
export default function SimpleI18nTestPage() {
  const [currentLang, setCurrentLang] = useState<Languages>(Languages.EN);

  const translations = {
    [Languages.EN]: {
      title: "Zenera - Simple i18n Test",
      description: "Testing basic internationalization",
      welcome: "Welcome to Zenera",
      switchLanguage: "Switch Language",
      currentLanguage: "Current Language",
      hello: "Hello World",
      goodbye: "Goodbye",
      loading: "Loading...",
      error: "An error occurred",
      success: "Success",
    },
    [Languages.VI]: {
      title: "Zenera - Test i18n Đơn Giản",
      description: "Kiểm tra quốc tế hóa cơ bản",
      welcome: "Chào mừng đến với Zenera",
      switchLanguage: "Chuyển Ngôn Ngữ",
      currentLanguage: "Ngôn <PERSON> Hiện Tại",
      hello: "Xi<PERSON>",
      goodbye: "<PERSON>ạ<PERSON> B<PERSON>t",
      loading: "<PERSON>ang tải...",
      error: "Đã xảy ra lỗi",
      success: "Thành công",
    },
  };

  const t = (key: string) => {
    return translations[currentLang][key as keyof typeof translations[Languages.EN]] || key;
  };

  const switchLanguage = () => {
    setCurrentLang(currentLang === Languages.EN ? Languages.VI : Languages.EN);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-2xl mx-auto px-4">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            {t("title")}
          </h1>
          <p className="text-gray-600">
            {t("description")}
          </p>
        </div>

        {/* Language Switcher */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold mb-2">{t("currentLanguage")}</h2>
              <p className="text-lg font-mono bg-gray-100 px-3 py-2 rounded inline-block">
                {currentLang} - {currentLang === Languages.EN ? "English" : "Tiếng Việt"}
              </p>
            </div>
            <button
              onClick={switchLanguage}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              {t("switchLanguage")}
            </button>
          </div>
        </div>

        {/* Content Examples */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Content Examples</h2>
          <div className="space-y-4">
            <div className="p-4 bg-gray-50 rounded">
              <h3 className="font-medium text-gray-900">{t("welcome")}</h3>
            </div>
            <div className="p-4 bg-blue-50 rounded">
              <h3 className="font-medium text-blue-900">{t("hello")}</h3>
            </div>
            <div className="p-4 bg-green-50 rounded">
              <h3 className="font-medium text-green-900">{t("success")}</h3>
            </div>
            <div className="p-4 bg-yellow-50 rounded">
              <h3 className="font-medium text-yellow-900">{t("loading")}</h3>
            </div>
            <div className="p-4 bg-red-50 rounded">
              <h3 className="font-medium text-red-900">{t("error")}</h3>
            </div>
            <div className="p-4 bg-purple-50 rounded">
              <h3 className="font-medium text-purple-900">{t("goodbye")}</h3>
            </div>
          </div>
        </div>

        {/* Language Flags */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-xl font-semibold mb-4">Language Selection</h2>
          <div className="flex space-x-4">
            <button
              onClick={() => setCurrentLang(Languages.EN)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-md border transition-colors ${
                currentLang === Languages.EN
                  ? "bg-blue-600 text-white border-blue-600"
                  : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
              }`}
            >
              <span className="text-lg">🇺🇸</span>
              <span>English</span>
            </button>
            <button
              onClick={() => setCurrentLang(Languages.VI)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-md border transition-colors ${
                currentLang === Languages.VI
                  ? "bg-blue-600 text-white border-blue-600"
                  : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
              }`}
            >
              <span className="text-lg">🇻🇳</span>
              <span>Tiếng Việt</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
