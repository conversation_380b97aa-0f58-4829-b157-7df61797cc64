"use client";

import { ProductCatalog } from "@/components/buyer/product-catalog";
import { HeroSection } from "@/components/buyer/hero-section";
import { FeaturedProducts } from "@/components/buyer/featured-products";
import { CategoryShowcase } from "@/components/buyer/category-showcase";

interface BuyerHomePageProps {
  params: {
    locale: string;
  };
}

export default function BuyerHomePage({ params: { locale } }: BuyerHomePageProps) {
  return (
    <div className="space-y-8">
      {/* Hero Section */}
      <HeroSection locale={locale} />
      
      {/* Category Showcase */}
      <CategoryShowcase locale={locale} />
      
      {/* Featured Products */}
      <FeaturedProducts locale={locale} />
      
      {/* Product Catalog */}
      <ProductCatalog locale={locale} />
    </div>
  );
}
