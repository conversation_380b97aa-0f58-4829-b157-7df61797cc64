"use client";

import { ReactNode } from "react";
import { BuyerHeader } from "@/components/layouts/buyer-header";
import { BuyerFooter } from "@/components/layouts/buyer-footer";
import { BuyerNavigation } from "@/components/layouts/buyer-navigation";

interface BuyerLayoutProps {
  children: ReactNode;
  params: {
    locale: string;
  };
}

export default function BuyerLayout({ 
  children, 
  params: { locale } 
}: BuyerLayoutProps) {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header với navigation */}
      <BuyerHeader locale={locale} />
      
      {/* Navigation menu */}
      <BuyerNavigation locale={locale} />
      
      {/* Main content */}
      <main className="container mx-auto px-4 py-8">
        {children}
      </main>
      
      {/* Footer */}
      <BuyerFooter locale={locale} />
    </div>
  );
}
