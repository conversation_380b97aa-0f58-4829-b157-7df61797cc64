#!/bin/bash
# Zenera Development Start Script
# Tham khảo từ Medoo patterns

cwd=$(pwd)

echo "🚀 Starting Zenera Development Environment..."

# Function to check if port is available
check_port() {
  local port=$1
  if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null ; then
    return 1
  else
    return 0
  fi
}

# Function to find available port
find_available_port() {
  local start_port=$1
  local max_port=$((start_port + 10))
  
  for port in $(seq $start_port $max_port); do
    if check_port $port; then
      echo $port
      return 0
    fi
  done
  
  echo "No available port found in range $start_port-$max_port"
  return 1
}

# Start sharing package build watch
echo "📦 Building sharing package..."
cd ${cwd}/packages/sharing/
pnpm build:watch & WatchSharing=$!

# Start API server
echo "🔧 Starting API server..."
cd ${cwd}/packages/api-server/
API_PORT=$(find_available_port 3001)
if [ $? -eq 0 ]; then
  echo "API server will start on port $API_PORT"
  PORT=$API_PORT pnpm dev & StartApi=$!
else
  echo "❌ Could not find available port for API server"
  exit 1
fi

# Start webapp
echo "🌐 Starting webapp..."
cd ${cwd}/packages/webapp/
WEBAPP_PORT=$(find_available_port 3002)
if [ $? -eq 0 ]; then
  echo "Webapp will start on port $WEBAPP_PORT"
  PORT=$WEBAPP_PORT pnpm dev & StartWebapp=$!
else
  echo "❌ Could not find available port for webapp"
  exit 1
fi

echo "✅ All services started successfully!"
echo "📱 Webapp: http://localhost:$WEBAPP_PORT"
echo "🔧 API: http://localhost:$API_PORT"
echo "📚 API Docs: http://localhost:$API_PORT/api"

# Wait for all processes
wait $WatchSharing $StartApi $StartWebapp
