#!/bin/bash
# Zenera Services Check Script
# Tham khảo từ Medoo patterns

echo "🔍 Checking Zenera Services Status..."

echo ""
echo "=== Node.js Processes ==="
ps -ax | grep node | grep -v grep | head -10

echo ""
echo "=== Port Usage ==="
echo "Port 3001 (API Server):"
lsof -i :3001 | head -5

echo ""
echo "Port 3002 (Webapp):"
lsof -i :3002 | head -5

echo ""
echo "Port 3003 (Alternative API):"
lsof -i :3003 | head -5

echo ""
echo "=== MongoDB Connection ==="
if command -v mongosh >/dev/null 2>&1; then
  echo "✅ MongoDB CLI (mongosh) available"
else
  echo "⚠️  MongoDB CLI (mongosh) not found"
fi

echo ""
echo "=== System Resources ==="
echo "Memory usage:"
free -h 2>/dev/null || vm_stat | head -5

echo ""
echo "Disk usage:"
df -h . | head -2

echo ""
echo "=== Environment Check ==="
echo "Node.js version: $(node --version 2>/dev/null || echo 'Not found')"
echo "pnpm version: $(pnpm --version 2>/dev/null || echo 'Not found')"
echo "Current directory: $(pwd)"

echo ""
echo "=== Package Status ==="
if [ -f "package.json" ]; then
  echo "✅ Root package.json found"
else
  echo "❌ Root package.json not found"
fi

if [ -f "packages/webapp/package.json" ]; then
  echo "✅ Webapp package.json found"
else
  echo "❌ Webapp package.json not found"
fi

if [ -f "packages/api-server/package.json" ]; then
  echo "✅ API server package.json found"
else
  echo "❌ API server package.json not found"
fi

echo ""
echo "🏁 Service check completed!"
