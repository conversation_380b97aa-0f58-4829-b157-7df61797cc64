#!/bin/bash
# Zenera Setup Script
# Tham khảo từ Medoo patterns

cwd=$(pwd)

echo "🚀 Setting up Zenera Development Environment..."

# Function to check if command exists
command_exists() {
  command -v "$1" >/dev/null 2>&1
}

# Check required tools
echo "🔍 Checking required tools..."

if ! command_exists node; then
  echo "❌ Node.js is not installed. Please install Node.js 18+ first."
  exit 1
else
  NODE_VERSION=$(node --version)
  echo "✅ Node.js found: $NODE_VERSION"
fi

if ! command_exists pnpm; then
  echo "📦 Installing pnpm..."
  npm install -g pnpm
  if [ $? -eq 0 ]; then
    echo "✅ pnpm installed successfully"
  else
    echo "❌ Failed to install pnpm"
    exit 1
  fi
else
  PNPM_VERSION=$(pnpm --version)
  echo "✅ pnpm found: $PNPM_VERSION"
fi

# Install dependencies
echo "📦 Installing dependencies..."
pnpm install

if [ $? -eq 0 ]; then
  echo "✅ Dependencies installed successfully"
else
  echo "❌ Failed to install dependencies"
  exit 1
fi

# Build sharing package first
echo "🏗️  Building shared packages..."
cd ${cwd}/packages/sharing
pnpm build

cd ${cwd}/packages/ui-components
pnpm build

# Setup environment files if they don't exist
echo "⚙️  Setting up environment files..."

# API server environment
if [ ! -f "${cwd}/packages/api-server/.env" ]; then
  echo "📝 Creating API server .env file..."
  cat > ${cwd}/packages/api-server/.env << EOF
# Database
MONGODB_URI=mongodb+srv://zenera:<EMAIL>/?retryWrites=true&w=majority&appName=Zenera-cluster

# JWT
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=your-super-secret-refresh-key-change-in-production
JWT_REFRESH_EXPIRES_IN=30d

# Server
PORT=3001
NODE_ENV=development

# CORS
CORS_ORIGIN=http://localhost:3002,http://localhost:3000

# File Upload
UPLOAD_DEST=./uploads
MAX_FILE_SIZE=10485760
EOF
  echo "✅ API server .env created"
fi

# Webapp environment
if [ ! -f "${cwd}/packages/webapp/.env.local" ]; then
  echo "📝 Creating webapp .env.local file..."
  cat > ${cwd}/packages/webapp/.env.local << EOF
# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:3001
NEXT_PUBLIC_APP_URL=http://localhost:3002

# Environment
NODE_ENV=development
EOF
  echo "✅ Webapp .env.local created"
fi

echo "🎉 Setup completed successfully!"
echo ""
echo "📋 Next steps:"
echo "  1. Run 'bin/start' to start all services"
echo "  2. Run 'bin/start_frontend' to start frontend only"
echo "  3. Run 'bin/start_backend' to start backend only"
echo "  4. Run 'bin/build' to build all packages"
echo ""
echo "🌐 Default URLs:"
echo "  Frontend: http://localhost:3002"
echo "  Backend: http://localhost:3001"
echo "  API Docs: http://localhost:3001/api"
