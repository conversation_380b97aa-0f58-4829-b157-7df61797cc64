#!/bin/bash
# Zenera Build Script
# Tham khảo từ Medoo patterns

cwd=$(pwd)

echo "🏗️  Building Zenera Project..."

# Function to build sharing package
build_sharing() {
  echo "📦 Building sharing package..."
  cd ${cwd}/packages/sharing
  pnpm install
  pnpm build
  if [ $? -eq 0 ]; then
    echo "✅ Sharing package built successfully"
  else
    echo "❌ Failed to build sharing package"
    exit 1
  fi
}

# Function to build API server
build_api_server() {
  echo "🔧 Building API server..."
  cd ${cwd}/packages/api-server/
  pnpm install
  pnpm build
  if [ $? -eq 0 ]; then
    echo "✅ API server built successfully"
  else
    echo "❌ Failed to build API server"
    exit 1
  fi
}

# Function to build webapp
build_webapp() {
  echo "🌐 Building webapp..."
  cd ${cwd}/packages/webapp
  pnpm install
  pnpm build
  if [ $? -eq 0 ]; then
    echo "✅ Webapp built successfully"
  else
    echo "❌ Failed to build webapp"
    exit 1
  fi
}

# Function to build UI components
build_ui_components() {
  echo "🎨 Building UI components..."
  cd ${cwd}/packages/ui-components
  pnpm install
  pnpm build
  if [ $? -eq 0 ]; then
    echo "✅ UI components built successfully"
  else
    echo "❌ Failed to build UI components"
    exit 1
  fi
}

# Main build process
echo "🚀 Starting build process..."

# Install root dependencies
echo "📦 Installing root dependencies..."
pnpm install

# Build in correct order (dependencies first)
build_sharing
build_ui_components
build_api_server
build_webapp

echo "🎉 All packages built successfully!"
echo "📊 Build Summary:"
echo "  ✅ Sharing package"
echo "  ✅ UI components"
echo "  ✅ API server"
echo "  ✅ Webapp"
